<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleSync"
          v-hasPermi="['order:order:add']"
        >同步</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['order:order:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['order:order:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['order:order:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单号" align="center" prop="orderId" />
      <el-table-column label="订单类型" align="center" prop="orderType" >
        <template slot-scope="scope">
          <span>{{ scope.row.orderType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="orderStatus" />
      <el-table-column label="售后状态" align="center" prop="orderAfterSalesStatus" />
      <el-table-column label="申请取消状态" align="center" prop="cancelStatus" />
      <el-table-column label="创建时间" align="center" prop="createdTime" >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付时间" align="center" prop="paidTime" >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单发货时间" align="center" prop="deliveryTime" >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单取消时间" align="center" prop="cancelTime" >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单完成时间" align="center" prop="finishTime" >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="承诺最晚发货时间" align="center" prop="promiseLastDeliveryTime" >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物流方案id" align="center" prop="planinfoId" />
      <el-table-column label="物流方案名称" align="center" prop="planinfoName" />
      <el-table-column label="收件人国家id" align="center" prop="receiverCountryId" />
      <el-table-column label="目前仅 中国" align="center" prop="receiverCountryName" />
      <el-table-column label="收件人省份id" align="center" prop="receiverProvinceId" />
      <el-table-column label="收件人省份" align="center" prop="receiverProvinceName" />
      <el-table-column label="收件人城市id" align="center" prop="receiverCityId" />
      <el-table-column label="收件人城市" align="center" prop="receiverCityName" />
      <el-table-column label="收件人区县id" align="center" prop="receiverDistrictId" />
      <el-table-column label="收件人区县名称" align="center" prop="receiverDistrictName" />
      <el-table-column label="用户备注" align="center" prop="customerRemark" />
      <el-table-column label="商家标记备注" align="center" prop="sellerRemark" />
      <el-table-column label="商家标记优先级" align="center" prop="sellerRemarkFlag" />
      <el-table-column label="原始订单编号" align="center" prop="originalOrderId" />
      <el-table-column label="物流模式" align="center" prop="logistics" />
      <el-table-column label="订单标签列表" align="center" prop="orderTagList" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['order:order:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['order:order:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改小红书订单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="创建时间" prop="createdTime">
          <el-input v-model="form.createdTime" placeholder="请输入创建时间" />
        </el-form-item>
        <el-form-item label="支付时间" prop="paidTime">
          <el-input v-model="form.paidTime" placeholder="请输入支付时间" />
        </el-form-item>
        <el-form-item label="订单发货时间" prop="deliveryTime">
          <el-input v-model="form.deliveryTime" placeholder="请输入订单发货时间" />
        </el-form-item>
        <el-form-item label="订单取消时间" prop="cancelTime">
          <el-input v-model="form.cancelTime" placeholder="请输入订单取消时间" />
        </el-form-item>
        <el-form-item label="订单完成时间" prop="finishTime">
          <el-input v-model="form.finishTime" placeholder="请输入订单完成时间" />
        </el-form-item>
        <el-form-item label="承诺最晚发货时间" prop="promiseLastDeliveryTime">
          <el-input v-model="form.promiseLastDeliveryTime" placeholder="请输入承诺最晚发货时间" />
        </el-form-item>
        <el-form-item label="物流方案id" prop="planinfoId">
          <el-input v-model="form.planinfoId" placeholder="请输入物流方案id" />
        </el-form-item>
        <el-form-item label="物流方案名称" prop="planinfoName">
          <el-input v-model="form.planinfoName" placeholder="请输入物流方案名称" />
        </el-form-item>
        <el-form-item label="收件人国家id" prop="receiverCountryId">
          <el-input v-model="form.receiverCountryId" placeholder="请输入收件人国家id" />
        </el-form-item>
        <el-form-item label="目前仅 中国" prop="receiverCountryName">
          <el-input v-model="form.receiverCountryName" placeholder="请输入目前仅 中国" />
        </el-form-item>
        <el-form-item label="收件人省份id" prop="receiverProvinceId">
          <el-input v-model="form.receiverProvinceId" placeholder="请输入收件人省份id" />
        </el-form-item>
        <el-form-item label="收件人省份" prop="receiverProvinceName">
          <el-input v-model="form.receiverProvinceName" placeholder="请输入收件人省份" />
        </el-form-item>
        <el-form-item label="收件人城市id" prop="receiverCityId">
          <el-input v-model="form.receiverCityId" placeholder="请输入收件人城市id" />
        </el-form-item>
        <el-form-item label="收件人城市" prop="receiverCityName">
          <el-input v-model="form.receiverCityName" placeholder="请输入收件人城市" />
        </el-form-item>
        <el-form-item label="收件人区县id" prop="receiverDistrictId">
          <el-input v-model="form.receiverDistrictId" placeholder="请输入收件人区县id" />
        </el-form-item>
        <el-form-item label="收件人区县名称" prop="receiverDistrictName">
          <el-input v-model="form.receiverDistrictName" placeholder="请输入收件人区县名称" />
        </el-form-item>
        <el-form-item label="用户备注" prop="customerRemark">
          <el-input v-model="form.customerRemark" placeholder="请输入用户备注" />
        </el-form-item>
        <el-form-item label="商家标记备注" prop="sellerRemark">
          <el-input v-model="form.sellerRemark" placeholder="请输入商家标记备注" />
        </el-form-item>
        <el-form-item label="商家标记优先级" prop="sellerRemarkFlag">
          <el-input v-model="form.sellerRemarkFlag" placeholder="请输入商家标记优先级" />
        </el-form-item>
        <el-form-item label="原始订单编号" prop="originalOrderId">
          <el-input v-model="form.originalOrderId" placeholder="请输入原始订单编号" />
        </el-form-item>
        <el-form-item label="物流模式" prop="logistics">
          <el-input v-model="form.logistics" placeholder="请输入物流模式" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrder, getOrder, delOrder, syncOrder, updateOrder } from "@/api/order/order"

export default {
  name: "Order",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 小红书订单表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 同步参数
      day: 7,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderType: null,
        orderStatus: null,
        orderAfterSalesStatus: null,
        cancelStatus: null,
        createdTime: null,
        paidTime: null,
        deliveryTime: null,
        cancelTime: null,
        finishTime: null,
        promiseLastDeliveryTime: null,
        planinfoId: null,
        planinfoName: null,
        receiverCountryId: null,
        receiverCountryName: null,
        receiverProvinceId: null,
        receiverProvinceName: null,
        receiverCityId: null,
        receiverCityName: null,
        receiverDistrictId: null,
        receiverDistrictName: null,
        customerRemark: null,
        sellerRemark: null,
        sellerRemarkFlag: null,
        originalOrderId: null,
        logistics: null,
        orderTagList: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        orderType: [
          { required: true, message: "订单类型", trigger: "change" }
        ],
        orderStatus: [
          { required: true, message: "订单状态", trigger: "change" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询小红书订单列表 */
    getList() {
      this.loading = true
      listOrder(this.queryParams).then(response => {
        this.orderList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        orderId: null,
        orderType: null,
        orderStatus: null,
        orderAfterSalesStatus: null,
        cancelStatus: null,
        createdTime: null,
        paidTime: null,
        updateTime: null,
        deliveryTime: null,
        cancelTime: null,
        finishTime: null,
        promiseLastDeliveryTime: null,
        planinfoId: null,
        planinfoName: null,
        receiverCountryId: null,
        receiverCountryName: null,
        receiverProvinceId: null,
        receiverProvinceName: null,
        receiverCityId: null,
        receiverCityName: null,
        receiverDistrictId: null,
        receiverDistrictName: null,
        customerRemark: null,
        sellerRemark: null,
        sellerRemarkFlag: null,
        originalOrderId: null,
        logistics: null,
        orderTagList: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.orderId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 同步按钮操作 */
    handleSync() {
      syncOrder(this.day).then(response => {
        this.$modal.msgSuccess("同步成功")
        this.open = false
        this.getList()
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const orderId = row.orderId || this.ids
      getOrder(orderId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改小红书订单"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.orderId != null) {
            updateOrder(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            /*addOrder(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })*/
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const orderIds = row.orderId || this.ids
      this.$modal.confirm('是否确认删除小红书订单编号为"' + orderIds + '"的数据项？').then(function() {
        return delOrder(orderIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('order/order/export', {
        ...this.queryParams
      }, `order_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
