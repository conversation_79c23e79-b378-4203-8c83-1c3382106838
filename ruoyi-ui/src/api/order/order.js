import request from '@/utils/request'

// 查询小红书订单列表
export function listOrder(query) {
  return request({
    url: '/order/order/list',
    method: 'get',
    params: query
  })
}

// 查询小红书订单详细
export function getOrder(orderId) {
  return request({
    url: '/order/order/' + orderId,
    method: 'get'
  })
}

// 同步小红书订单
export function syncOrder(day) {
  return request({
    url: '/order/order/sync/'+day,
    timeout: 60000000,
    method: 'get',
  })
}

// 修改小红书订单
export function updateOrder(data) {
  return request({
    url: '/order/order',
    method: 'put',
    data: data
  })
}

// 删除小红书订单
export function delOrder(orderId) {
  return request({
    url: '/order/order/' + orderId,
    method: 'delete'
  })
}
