<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.order.mapper.ShellerUserMapper">
    
    <resultMap type="ShellerUser" id="ShellerUserResult">
        <result property="shellerId"    column="sheller_id"    />
        <result property="userId"    column="user_id"    />
    </resultMap>

    <sql id="selectShellerUserVo">
        select sheller_id, user_id from t_sheller_user
    </sql>

    <select id="selectShellerUserList" parameterType="ShellerUser" resultMap="ShellerUserResult">
        <include refid="selectShellerUserVo"/>
        <where>  
            <if test="shellerId != null  and shellerId != ''"> and sheller_id = #{shellerId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>
    
    <select id="selectShellerUserByShellerId" parameterType="String" resultMap="ShellerUserResult">
        <include refid="selectShellerUserVo"/>
        where sheller_id = #{shellerId}
    </select>
    <select id="selectShellerUserByUserId" resultType="string">
        select sheller_id from t_sheller_user where user_id = #{userId}
    </select>

    <insert id="insertShellerUser" parameterType="ShellerUser">
        insert into t_sheller_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shellerId != null and shellerId != ''">sheller_id,</if>
            <if test="userId != null">user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shellerId != null and shellerId != ''">#{shellerId},</if>
            <if test="userId != null">#{userId},</if>
         </trim>
    </insert>

    <update id="updateShellerUser" parameterType="ShellerUser">
        update t_sheller_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where sheller_id = #{shellerId}
    </update>

    <delete id="deleteShellerUserByShellerId" parameterType="String">
        delete from t_sheller_user where sheller_id = #{shellerId}
    </delete>

    <delete id="deleteShellerUserByShellerIds" parameterType="String">
        delete from t_sheller_user where sheller_id in 
        <foreach item="shellerId" collection="array" open="(" separator="," close=")">
            #{shellerId}
        </foreach>
    </delete>
</mapper>