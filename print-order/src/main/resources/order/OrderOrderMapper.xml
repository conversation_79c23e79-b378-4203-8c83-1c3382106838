<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.order.mapper.OrderOrderMapper">
    
    <resultMap type="OrderOrder" id="OrderOrderResult">
        <result property="orderId"    column="order_id"    />
        <result property="orderType"    column="order_type"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="orderAfterSalesStatus"    column="order_after_sales_status"    />
        <result property="cancelStatus"    column="cancel_status"    />
        <result property="createdTime"    column="created_time"    />
        <result property="paidTime"    column="paid_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deliveryTime"    column="delivery_time"    />
        <result property="cancelTime"    column="cancel_time"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="promiseLastDeliveryTime"    column="promise_last_delivery_time"    />
        <result property="planinfoId"    column="planInfo_id"    />
        <result property="planinfoName"    column="planInfo_name"    />
        <result property="receiverCountryId"    column="receiver_country_id"    />
        <result property="receiverCountryName"    column="receiver_country_name"    />
        <result property="receiverProvinceId"    column="receiver_province_id"    />
        <result property="receiverProvinceName"    column="receiver_province_name"    />
        <result property="receiverCityId"    column="receiver_city_id"    />
        <result property="receiverCityName"    column="receiver_city_name"    />
        <result property="receiverDistrictId"    column="receiver_district_id"    />
        <result property="receiverDistrictName"    column="receiver_district_name"    />
        <result property="customerRemark"    column="customer_remark"    />
        <result property="sellerRemark"    column="seller_remark"    />
        <result property="sellerRemarkFlag"    column="seller_remark_flag"    />
        <result property="originalOrderId"    column="original_order_id"    />
        <result property="logistics"    column="logistics"    />
        <result property="orderTagList"    column="order_tag_list"    />
        <result property="userId"    column="user_id"    />
        <result property="openAddressId"    column="open_address_id"    />
        <result property="receiverName"    column="receiver_name"    />
        <result property="receiverPhone"    column="receiver_phone"    />
        <result property="receiverAddress"    column="receiver_address"    />
    </resultMap>

    <sql id="selectOrderOrderVo">
        select order_id, order_type, order_status, order_after_sales_status, cancel_status, created_time, paid_time, update_time, delivery_time, cancel_time, finish_time, promise_last_delivery_time, planInfo_id, planInfo_name, receiver_country_id, receiver_country_name, receiver_province_id, receiver_province_name, receiver_city_id, receiver_city_name, receiver_district_id, receiver_district_name, customer_remark, seller_remark, seller_remark_flag, original_order_id, logistics, order_tag_list, user_id, open_address_id, receiver_name, receiver_phone, receiver_address from ${tableName}
    </sql>

    <select id="selectOrderOrderList" parameterType="OrderOrder" resultMap="OrderOrderResult">
        <include refid="selectOrderOrderVo"/>
        <where>  
            <if test="orderOrder.orderType != null "> and order_type = #{orderOrder.orderType}</if>
            <if test="orderOrder.orderStatus != null "> and order_status = #{orderOrder.orderStatus}</if>
            <if test="orderOrder.cancelStatus != null "> and cancel_status = #{orderOrder.cancelStatus}</if>
            <if test="orderOrder.createdTime != null "> and created_time = #{orderOrder.createdTime}</if>
            <if test="orderOrder.paidTime != null "> and paid_time = #{orderOrder.paidTime}</if>
        </where>
    </select>
    
    <select id="selectOrderOrderByOrderId" parameterType="String" resultMap="OrderOrderResult">
        <include refid="selectOrderOrderVo"/>
        where order_id = #{orderId}
    </select>

    <insert id="insertOrderOrder" parameterType="OrderOrder">
        insert into t_order_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="orderType != null">order_type,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="orderAfterSalesStatus != null">order_after_sales_status,</if>
            <if test="cancelStatus != null">cancel_status,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="paidTime != null">paid_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deliveryTime != null">delivery_time,</if>
            <if test="cancelTime != null">cancel_time,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="promiseLastDeliveryTime != null">promise_last_delivery_time,</if>
            <if test="planinfoId != null">planInfo_id,</if>
            <if test="planinfoName != null">planInfo_name,</if>
            <if test="receiverCountryId != null">receiver_country_id,</if>
            <if test="receiverCountryName != null">receiver_country_name,</if>
            <if test="receiverProvinceId != null">receiver_province_id,</if>
            <if test="receiverProvinceName != null">receiver_province_name,</if>
            <if test="receiverCityId != null">receiver_city_id,</if>
            <if test="receiverCityName != null">receiver_city_name,</if>
            <if test="receiverDistrictId != null">receiver_district_id,</if>
            <if test="receiverDistrictName != null">receiver_district_name,</if>
            <if test="customerRemark != null">customer_remark,</if>
            <if test="sellerRemark != null">seller_remark,</if>
            <if test="sellerRemarkFlag != null">seller_remark_flag,</if>
            <if test="originalOrderId != null">original_order_id,</if>
            <if test="logistics != null">logistics,</if>
            <if test="orderTagList != null">order_tag_list,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="orderAfterSalesStatus != null">#{orderAfterSalesStatus},</if>
            <if test="cancelStatus != null">#{cancelStatus},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="paidTime != null">#{paidTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deliveryTime != null">#{deliveryTime},</if>
            <if test="cancelTime != null">#{cancelTime},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="promiseLastDeliveryTime != null">#{promiseLastDeliveryTime},</if>
            <if test="planinfoId != null">#{planinfoId},</if>
            <if test="planinfoName != null">#{planinfoName},</if>
            <if test="receiverCountryId != null">#{receiverCountryId},</if>
            <if test="receiverCountryName != null">#{receiverCountryName},</if>
            <if test="receiverProvinceId != null">#{receiverProvinceId},</if>
            <if test="receiverProvinceName != null">#{receiverProvinceName},</if>
            <if test="receiverCityId != null">#{receiverCityId},</if>
            <if test="receiverCityName != null">#{receiverCityName},</if>
            <if test="receiverDistrictId != null">#{receiverDistrictId},</if>
            <if test="receiverDistrictName != null">#{receiverDistrictName},</if>
            <if test="customerRemark != null">#{customerRemark},</if>
            <if test="sellerRemark != null">#{sellerRemark},</if>
            <if test="sellerRemarkFlag != null">#{sellerRemarkFlag},</if>
            <if test="originalOrderId != null">#{originalOrderId},</if>
            <if test="logistics != null">#{logistics},</if>
            <if test="orderTagList != null">#{orderTagList},</if>
         </trim>
    </insert>
    <insert id="batchInsertOrderOrder">
        insert into t_order_order (order_id, order_type, order_status, order_after_sales_status, cancel_status, created_time, paid_time, update_time, delivery_time, cancel_time, finish_time, promise_last_delivery_time, planInfo_id, planInfo_name, receiver_country_id, receiver_country_name, receiver_province_id, receiver_province_name, receiver_city_id, receiver_city_name, receiver_district_id, receiver_district_name, customer_remark, seller_remark, seller_remark_flag, original_order_id, logistics, order_tag_list) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.orderId},#{item.orderType},#{item.orderStatus},#{item.orderAfterSalesStatus},#{item.cancelStatus},#{item.createdTime},#{item.paidTime},#{item.updateTime},#{item.deliveryTime},#{item.cancelTime},#{item.finishTime},#{item.promiseLastDeliveryTime},#{item.planinfoId},#{item.planinfoName},#{item.receiverCountryId},#{item.receiverCountryName},#{item.receiverProvinceId},#{item.receiverProvinceName},#{item.receiverCityId},#{item.receiverCityName},#{item.receiverDistrictId},#{item.receiverDistrictName},#{item.customerRemark},#{item.sellerRemark},#{item.sellerRemarkFlag},#{item.originalOrderId},#{item.logistics},#{item.orderTagList})
        </foreach>
    </insert>

    <!-- 动态表名批量插入订单 -->
    <insert id="batchInsertOrderOrderByTable">
        insert into ${tableName} (order_id, seller_id, order_type, order_status, order_after_sales_status, cancel_status, created_time, paid_time, update_time, delivery_time, cancel_time, finish_time, promise_last_delivery_time, planInfo_id, planInfo_name, receiver_country_id, receiver_country_name, receiver_province_id, receiver_province_name, receiver_city_id, receiver_city_name, receiver_district_id, receiver_district_name, customer_remark, seller_remark, seller_remark_flag, original_order_id, logistics, order_tag_list, user_id, open_address_id, receiver_name, receiver_phone, receiver_address) values
        <foreach item="item" index="index" collection="orderList" separator=",">
            (#{item.orderId},#{item.sellerId},#{item.orderType},#{item.orderStatus},#{item.orderAfterSalesStatus},#{item.cancelStatus},#{item.createdTime},#{item.paidTime},#{item.updateTime},#{item.deliveryTime},#{item.cancelTime},#{item.finishTime},#{item.promiseLastDeliveryTime},#{item.planinfoId},#{item.planinfoName},#{item.receiverCountryId},#{item.receiverCountryName},#{item.receiverProvinceId},#{item.receiverProvinceName},#{item.receiverCityId},#{item.receiverCityName},#{item.receiverDistrictId},#{item.receiverDistrictName},#{item.customerRemark},#{item.sellerRemark},#{item.sellerRemarkFlag},#{item.originalOrderId},#{item.logistics},#{item.orderTagList},
             #{item.userId},#{item.openAddressId},#{item.receiverName},#{item.receiverPhone},#{item.receiverAddress})
        </foreach>
    </insert>

    <!-- 创建分表 -->
    <update id="createOrderTable">
        CREATE TABLE IF NOT EXISTS ${tableName} (
            `order_id` varchar(100) NOT NULL COMMENT '订单号',
            `seller_id` varchar(100) DEFAULT NULL COMMENT '商户ID',
            `order_type` bigint(20) DEFAULT NULL COMMENT '订单类型，1普通 2定金预售 3全款预售(废弃) 4全款预售(新) 5换货补发',
            `order_status` bigint(20) DEFAULT NULL COMMENT '订单状态，1已下单待付款 2已支付处理中 3清关中 4待发货 5部分发货 6待收货 7已完成 8已关闭 9已取消 10换货申请中',
            `order_after_sales_status` bigint(20) DEFAULT NULL COMMENT '售后状态，1无售后 2售后处理中 3售后完成 4售后拒绝 5售后关闭 6平台介入中 7售后取消',
            `cancel_status` bigint(20) DEFAULT NULL COMMENT '申请取消状态，0未申请取消 1取消处理中',
            `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间(时间戳)',
            `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间(时间戳)',
            `paid_time` bigint(20) DEFAULT NULL COMMENT '支付时间(时间戳)',
            `delivery_time` bigint(20) DEFAULT NULL COMMENT '订单发货时间(时间戳)',
            `cancel_time` bigint(20) DEFAULT NULL COMMENT '订单取消时间(时间戳)',
            `finish_time` bigint(20) DEFAULT NULL COMMENT '订单完成时间(时间戳)',
            `promise_last_delivery_time` bigint(20) DEFAULT NULL COMMENT '承诺最晚发货时间(时间戳)',
            `planInfo_id` varchar(100) DEFAULT NULL COMMENT '物流方案id',
            `planInfo_name` varchar(200) DEFAULT NULL COMMENT '物流方案名称',
            `receiver_country_id` varchar(50) DEFAULT NULL COMMENT '收件人国家id',
            `receiver_country_name` varchar(100) DEFAULT NULL COMMENT '收件人国家名称',
            `receiver_province_id` varchar(50) DEFAULT NULL COMMENT '收件人省份id',
            `receiver_province_name` varchar(100) DEFAULT NULL COMMENT '收件人省份',
            `receiver_city_id` varchar(50) DEFAULT NULL COMMENT '收件人城市id',
            `receiver_city_name` varchar(100) DEFAULT NULL COMMENT '收件人城市',
            `receiver_district_id` varchar(50) DEFAULT NULL COMMENT '收件人区县id',
            `receiver_district_name` varchar(100) DEFAULT NULL COMMENT '收件人区县名称',
            `customer_remark` text COMMENT '用户备注',
            `seller_remark` text COMMENT '商家标记备注',
            `seller_remark_flag` bigint(20) DEFAULT NULL COMMENT '商家标记优先级，ark订单列表展示旗子颜色 1灰旗 2红旗 3黄旗 4绿旗 5蓝旗 6紫旗',
            `original_order_id` varchar(100) DEFAULT NULL COMMENT '原始订单编号，换货订单的原订单',
            `logistics` varchar(100) DEFAULT NULL COMMENT '物流模式',
            `order_tag_list` text COMMENT '订单标签列表',
            `is_auto_print` int(0) NOT NULL DEFAULT 0 COMMENT '是否自动打印',
            `is_printed` int(0) NOT NULL DEFAULT 0 COMMENT '是否已打印',
            `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '支付用户id',
            `open_address_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收件人姓名+手机+地址等计算得出，用来查询收件人详情',
            `receiver_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收件人姓名',
            `receiver_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收件人手机号码',
            `receiver_address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '收件人详细地址',
            PRIMARY KEY (`order_id`),
            KEY `idx_order_status` (`order_status`),
            KEY `idx_created_time` (`created_time`),
            KEY `idx_order_type` (`order_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小红书订单表_${sellerId}'
    </update>

    <update id="updateOrderOrder" parameterType="OrderOrder">
        update t_order_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="orderAfterSalesStatus != null">order_after_sales_status = #{orderAfterSalesStatus},</if>
            <if test="cancelStatus != null">cancel_status = #{cancelStatus},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="paidTime != null">paid_time = #{paidTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deliveryTime != null">delivery_time = #{deliveryTime},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="promiseLastDeliveryTime != null">promise_last_delivery_time = #{promiseLastDeliveryTime},</if>
            <if test="planinfoId != null">planInfo_id = #{planinfoId},</if>
            <if test="planinfoName != null">planInfo_name = #{planinfoName},</if>
            <if test="receiverCountryId != null">receiver_country_id = #{receiverCountryId},</if>
            <if test="receiverCountryName != null">receiver_country_name = #{receiverCountryName},</if>
            <if test="receiverProvinceId != null">receiver_province_id = #{receiverProvinceId},</if>
            <if test="receiverProvinceName != null">receiver_province_name = #{receiverProvinceName},</if>
            <if test="receiverCityId != null">receiver_city_id = #{receiverCityId},</if>
            <if test="receiverCityName != null">receiver_city_name = #{receiverCityName},</if>
            <if test="receiverDistrictId != null">receiver_district_id = #{receiverDistrictId},</if>
            <if test="receiverDistrictName != null">receiver_district_name = #{receiverDistrictName},</if>
            <if test="customerRemark != null">customer_remark = #{customerRemark},</if>
            <if test="sellerRemark != null">seller_remark = #{sellerRemark},</if>
            <if test="sellerRemarkFlag != null">seller_remark_flag = #{sellerRemarkFlag},</if>
            <if test="originalOrderId != null">original_order_id = #{originalOrderId},</if>
            <if test="logistics != null">logistics = #{logistics},</if>
            <if test="orderTagList != null">order_tag_list = #{orderTagList},</if>
        </trim>
        where order_id = #{orderId}
    </update>

    <delete id="deleteOrderOrderByOrderId" parameterType="String">
        delete from t_order_order where order_id = #{orderId}
    </delete>

    <delete id="deleteOrderOrderByOrderIds" parameterType="String">
        delete from t_order_order where order_id in 
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>
</mapper>