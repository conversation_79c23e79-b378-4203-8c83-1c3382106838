<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.order.mapper.OrderShellerMapper">
    
    <resultMap type="OrderSheller" id="TOrderShellerResult">
        <result property="sellerId"    column="seller_id"    />
        <result property="sellerName"    column="seller_name"    />
        <result property="expirationTime"    column="expiration_time"    />
        <result property="isPopup"    column="is_popup"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="bugVersion"    column="bug_version"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTOrderShellerVo">
        select seller_id, seller_name, expiration_time, is_popup, del_flag, bug_version, create_by, create_time, update_by, update_time from t_order_sheller
    </sql>

    <select id="selectTOrderShellerList" parameterType="OrderSheller" resultMap="TOrderShellerResult">
        <include refid="selectTOrderShellerVo"/>
        <where>
            and del_flag = '0'
            <if test="sellerName != null  and sellerName != ''"> and seller_name like concat('%', #{sellerName}, '%')</if>
            <if test="expirationTime != null "> and expiration_time = #{expirationTime}</if>
            <if test="isPopup != null "> and is_popup = #{isPopup}</if>
            <if test="bugVersion != null  and bugVersion != ''"> and bug_version = #{bugVersion}</if>
        </where>
    </select>
    
    <select id="selectTOrderShellerBySellerId" parameterType="String" resultMap="TOrderShellerResult">
        <include refid="selectTOrderShellerVo"/>
        where seller_id = #{sellerId} and del_flag = '0'
    </select>

    <insert id="insertTOrderSheller" parameterType="OrderSheller">
        insert into t_order_sheller
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sellerId != null">seller_id,</if>
            <if test="sellerName != null and sellerName != ''">seller_name,</if>
            <if test="expirationTime != null">expiration_time,</if>
            <if test="isPopup != null">is_popup,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="bugVersion != null and bugVersion != ''">bug_version,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sellerId != null">#{sellerId},</if>
            <if test="sellerName != null and sellerName != ''">#{sellerName},</if>
            <if test="expirationTime != null">#{expirationTime},</if>
            <if test="isPopup != null">#{isPopup},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="bugVersion != null and bugVersion != ''">#{bugVersion},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTOrderSheller" parameterType="OrderSheller">
        update t_order_sheller
        <trim prefix="SET" suffixOverrides=",">
            <if test="sellerName != null and sellerName != ''">seller_name = #{sellerName},</if>
            <if test="expirationTime != null">expiration_time = #{expirationTime},</if>
            <if test="isPopup != null">is_popup = #{isPopup},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="bugVersion != null and bugVersion != ''">bug_version = #{bugVersion},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where seller_id = #{sellerId}
    </update>

    <delete id="deleteTOrderShellerBySellerId" parameterType="String">
        delete from t_order_sheller where seller_id = #{sellerId}
    </delete>

    <delete id="deleteTOrderShellerBySellerIds" parameterType="String">
        delete from t_order_sheller where seller_id in 
        <foreach item="sellerId" collection="array" open="(" separator="," close=")">
            #{sellerId}
        </foreach>
    </delete>
</mapper>