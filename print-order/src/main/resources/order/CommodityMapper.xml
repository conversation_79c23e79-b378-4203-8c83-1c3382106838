<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.order.mapper.CommodityMapper">
    
    <resultMap type="Commodity" id="CommodityResult">
        <result property="skuId"    column="sku_id"    />
        <result property="skuName"    column="sku_name"    />
        <result property="erpCode"    column="erp_code"    />
        <result property="skuSpec"    column="sku_spec"    />
        <result property="skuImage"    column="sku_image"    />
        <result property="skuQuantity"    column="sku_quantity"    />
        <result property="totalPaidAmount"    column="total_paid_amount"    />
    </resultMap>

    <sql id="selectCommodityVo">
        select roder_id,sku_id, sku_name, erp_code, sku_spec, sku_image, sku_quantity, total_paid_amount from t_commodity
    </sql>

    <select id="selectCommodityList" parameterType="Commodity" resultMap="CommodityResult">
        <include refid="selectCommodityVo"/>
        <where>  
            <if test="skuName != null  and skuName != ''"> and sku_name like concat('%', #{skuName}, '%')</if>
            <if test="erpCode != null  and erpCode != ''"> and erp_code = #{erpCode}</if>
            <if test="skuSpec != null  and skuSpec != ''"> and sku_spec = #{skuSpec}</if>
            <if test="skuImage != null  and skuImage != ''"> and sku_image = #{skuImage}</if>
            <if test="skuQuantity != null  and skuQuantity != ''"> and sku_quantity = #{skuQuantity}</if>
            <if test="totalPaidAmount != null "> and total_paid_amount = #{totalPaidAmount}</if>
        </where>
    </select>
    
    <select id="selectCommodityBySkuId" parameterType="String" resultMap="CommodityResult">
        <include refid="selectCommodityVo"/>
        where sku_id = #{skuId}
    </select>

    <insert id="insertCommodity" parameterType="Commodity">
        insert into t_commodity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="skuId != null">sku_id,</if>
            <if test="skuName != null">sku_name,</if>
            <if test="erpCode != null">erp_code,</if>
            <if test="skuSpec != null">sku_spec,</if>
            <if test="skuImage != null">sku_image,</if>
            <if test="skuQuantity != null">sku_quantity,</if>
            <if test="totalPaidAmount != null">total_paid_amount,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="skuId != null">#{skuId},</if>
            <if test="skuName != null">#{skuName},</if>
            <if test="erpCode != null">#{erpCode},</if>
            <if test="skuSpec != null">#{skuSpec},</if>
            <if test="skuImage != null">#{skuImage},</if>
            <if test="skuQuantity != null">#{skuQuantity},</if>
            <if test="totalPaidAmount != null">#{totalPaidAmount},</if>
         </trim>
    </insert>
    <insert id="batchInsertCommodity">
        insert into t_commodity (order_id, sku_id, sku_name, erp_code, sku_spec, sku_image, sku_quantity, total_paid_amount)
        values
            <foreach item="item" index="index" collection="list" separator=",">
                (#{item.orderId}, #{item.skuId}, #{item.skuName}, #{item.erpCode}, #{item.skuSpec}, #{item.skuImage}, #{item.skuQuantity}, #{item.totalPaidAmount})
            </foreach>
    </insert>

    <update id="updateCommodity" parameterType="Commodity">
        update t_commodity
        <trim prefix="SET" suffixOverrides=",">
            <if test="skuName != null">sku_name = #{skuName},</if>
            <if test="erpCode != null">erp_code = #{erpCode},</if>
            <if test="skuSpec != null">sku_spec = #{skuSpec},</if>
            <if test="skuImage != null">sku_image = #{skuImage},</if>
            <if test="skuQuantity != null">sku_quantity = #{skuQuantity},</if>
            <if test="totalPaidAmount != null">total_paid_amount = #{totalPaidAmount},</if>
        </trim>
        where sku_id = #{skuId}
    </update>

    <delete id="deleteCommodityBySkuId" parameterType="String">
        delete from t_commodity where sku_id = #{skuId}
    </delete>

    <delete id="deleteCommodityBySkuIds" parameterType="String">
        delete from t_commodity where sku_id in 
        <foreach item="skuId" collection="array" open="(" separator="," close=")">
            #{skuId}
        </foreach>
    </delete>
</mapper>