package com.ruoyi.order.mapper;

import java.util.List;
import com.ruoyi.order.domain.Commodity;

/**
 * 商品Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
public interface CommodityMapper 
{
    /**
     * 查询商品
     * 
     * @param skuId 商品主键
     * @return 商品
     */
    public Commodity selectCommodityBySkuId(String skuId);

    /**
     * 查询商品列表
     * 
     * @param commodity 商品
     * @return 商品集合
     */
    public List<Commodity> selectCommodityList(Commodity commodity);

    /**
     * 新增商品
     * 
     * @param commodity 商品
     * @return 结果
     */
    public int insertCommodity(Commodity commodity);

    /**
     * 修改商品
     * 
     * @param commodity 商品
     * @return 结果
     */
    public int updateCommodity(Commodity commodity);

    /**
     * 删除商品
     * 
     * @param skuId 商品主键
     * @return 结果
     */
    public int deleteCommodityBySkuId(String skuId);

    /**
     * 批量删除商品
     * 
     * @param skuIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommodityBySkuIds(String[] skuIds);

    void batchInsertCommodity(List<Commodity> commodityList);
}
