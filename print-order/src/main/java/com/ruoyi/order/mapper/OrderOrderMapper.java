package com.ruoyi.order.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.order.domain.OrderOrder;

/**
 * 小红书订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public interface OrderOrderMapper 
{
    /**
     * 查询小红书订单
     * 
     * @param orderId 小红书订单主键
     * @return 小红书订单
     */
    public OrderOrder selectOrderOrderByOrderId(String orderId);

    /**
     * 查询小红书订单列表
     * 
     * @param orderOrder 小红书订单
     * @return 小红书订单集合
     */
    public List<OrderOrder> selectOrderOrderList(@Param("orderOrder") OrderOrder orderOrder,@Param("tableName") String tableName);

    /**
     * 新增小红书订单
     * 
     * @param orderOrder 小红书订单
     * @return 结果
     */
    public int insertOrderOrder(OrderOrder orderOrder);

    /**
     * 批量新增小红书订单
     *
     * @param orderOrder 小红书订单
     * @return 结果
     */
    public int batchInsertOrderOrder(List<OrderOrder> orderOrderList);

    /**
     * 修改小红书订单
     * 
     * @param orderOrder 小红书订单
     * @return 结果
     */
    public int updateOrderOrder(OrderOrder orderOrder);

    /**
     * 删除小红书订单
     * 
     * @param orderId 小红书订单主键
     * @return 结果
     */
    public int deleteOrderOrderByOrderId(String orderId);

    /**
     * 批量删除小红书订单
     *
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrderOrderByOrderIds(String[] orderIds);

    /**
     * 动态表名批量插入订单
     *
     * @param tableName 表名
     * @param orderList 订单列表
     * @return 结果
     */
    public int batchInsertOrderOrderByTable(@Param("tableName") String tableName, @Param("orderList") List<OrderOrder> orderList);

    /**
     * 创建订单分表
     *
     * @param tableName 表名
     * @param sellerId 商户ID
     * @return 结果
     */
    public int createOrderTable(@Param("tableName") String tableName, @Param("sellerId") String sellerId);
}
