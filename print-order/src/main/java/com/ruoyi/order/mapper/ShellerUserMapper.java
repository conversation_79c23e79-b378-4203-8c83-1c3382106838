package com.ruoyi.order.mapper;

import java.util.List;
import com.ruoyi.order.domain.ShellerUser;

/**
 * 商家用户中间Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public interface ShellerUserMapper 
{
    /**
     * 查询商家用户中间
     * 
     * @param shellerId 商家用户中间主键
     * @return 商家用户中间
     */
    public ShellerUser selectShellerUserByShellerId(String shellerId);

    /**
     * 查询商家用户中间列表
     * 
     * @param shellerUser 商家用户中间
     * @return 商家用户中间集合
     */
    public List<ShellerUser> selectShellerUserList(ShellerUser shellerUser);

    /**
     * 新增商家用户中间
     * 
     * @param shellerUser 商家用户中间
     * @return 结果
     */
    public int insertShellerUser(ShellerUser shellerUser);

    /**
     * 修改商家用户中间
     * 
     * @param shellerUser 商家用户中间
     * @return 结果
     */
    public int updateShellerUser(ShellerUser shellerUser);

    /**
     * 删除商家用户中间
     * 
     * @param shellerId 商家用户中间主键
     * @return 结果
     */
    public int deleteShellerUserByShellerId(String shellerId);

    /**
     * 批量删除商家用户中间
     * 
     * @param shellerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShellerUserByShellerIds(String[] shellerIds);

    String selectShellerUserByUserId(Long userId);
}
