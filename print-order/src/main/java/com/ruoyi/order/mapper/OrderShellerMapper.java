package com.ruoyi.order.mapper;

import java.util.List;
import com.ruoyi.order.domain.OrderSheller;

/**
 * 小红书商家信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface OrderShellerMapper
{
    /**
     * 查询小红书商家信息
     * 
     * @param sellerId 小红书商家信息主键
     * @return 小红书商家信息
     */
    public OrderSheller selectTOrderShellerBySellerId(String sellerId);

    /**
     * 查询小红书商家信息列表
     * 
     * @param orderSheller 小红书商家信息
     * @return 小红书商家信息集合
     */
    public List<OrderSheller> selectTOrderShellerList(OrderSheller orderSheller);

    /**
     * 新增小红书商家信息
     * 
     * @param orderSheller 小红书商家信息
     * @return 结果
     */
    public int insertTOrderSheller(OrderSheller orderSheller);

    /**
     * 修改小红书商家信息
     * 
     * @param orderSheller 小红书商家信息
     * @return 结果
     */
    public int updateTOrderSheller(OrderSheller orderSheller);

    /**
     * 删除小红书商家信息
     * 
     * @param sellerId 小红书商家信息主键
     * @return 结果
     */
    public int deleteTOrderShellerBySellerId(String sellerId);

    /**
     * 批量删除小红书商家信息
     * 
     * @param sellerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTOrderShellerBySellerIds(String[] sellerIds);
}
