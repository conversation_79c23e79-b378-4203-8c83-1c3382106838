package com.ruoyi.order.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.order.service.XiaoHongShuService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/order/xiaohongshu")
public class XiaoHongShuController {

    @Autowired
    private XiaoHongShuService xiaoHongShuService;

    /**
     * 小红书回调地址（外部回调，无用户上下文）
     */
    @RequestMapping("/redirect")
    public void redirect(String code) {
        xiaoHongShuService.redirect(code,"oauth.getAccessToken");
    }



}
