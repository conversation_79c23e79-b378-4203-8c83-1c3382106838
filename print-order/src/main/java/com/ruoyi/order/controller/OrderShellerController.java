package com.ruoyi.order.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.order.domain.OrderSheller;
import com.ruoyi.order.service.OrderShellerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小红书商家信息Controller
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/sheller/sheller")
public class OrderShellerController extends BaseController
{
    @Autowired
    private OrderShellerService tOrderShellerService;

    /**
     * 查询小红书商家信息列表
     */
    @PreAuthorize("@ss.hasPermi('sheller:sheller:list')")
    @GetMapping("/list")
    public TableDataInfo list(OrderSheller orderSheller)
    {
        startPage();
        List<OrderSheller> list = tOrderShellerService.selectTOrderShellerList(orderSheller);
        return getDataTable(list);
    }

    /**
     * 导出小红书商家信息列表
     */
    @PreAuthorize("@ss.hasPermi('sheller:sheller:export')")
    @Log(title = "小红书商家信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrderSheller orderSheller)
    {
        List<OrderSheller> list = tOrderShellerService.selectTOrderShellerList(orderSheller);
        ExcelUtil<OrderSheller> util = new ExcelUtil<OrderSheller>(OrderSheller.class);
        util.exportExcel(response, list, "小红书商家信息数据");
    }

    /**
     * 获取小红书商家信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('sheller:sheller:query')")
    @GetMapping(value = "/{sellerId}")
    public AjaxResult getInfo(@PathVariable("sellerId") String sellerId)
    {
        return success(tOrderShellerService.selectTOrderShellerBySellerId(sellerId));
    }

    /**
     * 新增小红书商家信息
     */
    @PreAuthorize("@ss.hasPermi('sheller:sheller:add')")
    @Log(title = "小红书商家信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrderSheller orderSheller)
    {
        return toAjax(tOrderShellerService.insertTOrderSheller(orderSheller));
    }

    /**
     * 修改小红书商家信息
     */
    @PreAuthorize("@ss.hasPermi('sheller:sheller:edit')")
    @Log(title = "小红书商家信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrderSheller orderSheller)
    {
        return toAjax(tOrderShellerService.updateTOrderSheller(orderSheller));
    }

    /**
     * 删除小红书商家信息
     */
    @PreAuthorize("@ss.hasPermi('sheller:sheller:remove')")
    @Log(title = "小红书商家信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sellerIds}")
    public AjaxResult remove(@PathVariable String[] sellerIds)
    {
        return toAjax(tOrderShellerService.deleteTOrderShellerBySellerIds(sellerIds));
    }
}
