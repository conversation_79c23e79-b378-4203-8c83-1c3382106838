package com.ruoyi.order.controller;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.order.domain.vo.OrderVO;
import com.ruoyi.order.service.XiaoHongShuService;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.order.domain.OrderOrder;
import com.ruoyi.order.service.IOrderOrderService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小红书订单Controller
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
@RestController
@RequestMapping("/order/order")
public class OrderOrderController extends BaseController
{
    @Autowired
    private IOrderOrderService orderOrderService;
    @Autowired
    private XiaoHongShuService xiaoHongShuService;

    /**
     * 查询小红书订单列表
     */
    @PreAuthorize("@ss.hasPermi('order:order:list')")
    @GetMapping("/list")
    public PageInfo<OrderVO> list(OrderOrder orderOrder)
    {

        List<OrderOrder> list = orderOrderService.selectOrderOrderList(orderOrder);
        PageInfo<OrderOrder> pageInfo = new PageInfo<>(list);
        List<OrderVO> orderVOS= new ArrayList<>();

        for (OrderOrder order : list) {
            OrderVO orderVO = new OrderVO();
            //将order.getPromiseLastDeliveryTime()-order.getPaidTime()转为LocalDateTime
            Long time = order.getPromiseLastDeliveryTime() - order.getPaidTime();
            LocalDateTime promiseLastDeliveryTime = LocalDateTime.ofEpochSecond(time, 0, ZoneOffset.UTC);
            orderVO.setPromiseLastDeliveryTime(promiseLastDeliveryTime);
            orderVO.setOrderId(order.getOrderId());
            //将小红书加密数据进行脱敏
            JSONObject jsonObject = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            if(order.getReceiverName()==null&&order.getReceiverPhone()==null&&order.getReceiverAddress()==null){
                orderVOS.add(orderVO);
                continue;
            }
            for (int i = 0; i < 3; i++) {
                JSONObject baseInfosJson = new JSONObject();
                baseInfosJson.set("dataTag", order.getOrderId());
                if (i == 0) {
                    baseInfosJson.set("encryptedData", order.getReceiverName());
                }
                if (i == 1) {
                    baseInfosJson.set("encryptedData", order.getReceiverPhone());
                }
                if (i == 2) {
                    baseInfosJson.set("encryptedData", order.getReceiverAddress());
                }
                jsonArray.add(baseInfosJson);
            }
            jsonObject.set("baseInfos", jsonArray);
            jsonObject.set("actionType", "1");
            //小红书废弃字段 必传 但是可以随意传任意内容
            jsonObject.set("appUserId", 111111);
            String decryptResponseBodyStr = xiaoHongShuService.xiaoHongShuSend("data.batchDesensitise", jsonObject);
            JSONObject decryptResponseBody = JSONUtil.parseObj(decryptResponseBodyStr);
            if (!decryptResponseBody.getBool("success")) {
                throw new ServiceException("小红书解密接口异常");
            }
            JSONArray dataInfoList = decryptResponseBody.getJSONObject("data").getJSONArray("desensitiseInfoList");
            for (int i = 0; i < 3; i++) {
                if (dataInfoList.getJSONObject(i) == null) {
                    continue;
                }
                if (dataInfoList.getJSONObject(i).getStr("desensitisedData") == null) {
                    continue;
                }
                if (i == 0) {
                    orderVO.setReceiverName(dataInfoList.getJSONObject(i).getStr("desensitisedData"));
                }
                if (i == 1) {
                    orderVO.setReceiverPhone(dataInfoList.getJSONObject(i).getStr("desensitisedData"));
                }
                if (i == 2) {
                    orderVO.setReceiverAddress(dataInfoList.getJSONObject(i).getStr("desensitisedData"));
                }
            }
            orderVOS.add(orderVO);
        }
        PageInfo<OrderVO> page= new PageInfo<>(orderVOS);
        BeanUtils.copyProperties(pageInfo,page);
        return page;
    }

    /**
     * 导出小红书订单列表
     */
    @PreAuthorize("@ss.hasPermi('order:order:export')")
    @Log(title = "小红书订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrderOrder orderOrder)
    {
        List<OrderOrder> list = orderOrderService.selectOrderOrderList(orderOrder);
        ExcelUtil<OrderOrder> util = new ExcelUtil<OrderOrder>(OrderOrder.class);
        util.exportExcel(response, list, "小红书订单数据");
    }

    /**
     * 获取小红书订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('order:order:query')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") String orderId)
    {
        return success(orderOrderService.selectOrderOrderByOrderId(orderId));
    }

    /**
     * 全量同步小红书订单
     */
    @PreAuthorize("@ss.hasPermi('order:order:add')")
    @Log(title = "小红书订单", businessType = BusinessType.INSERT)
    @GetMapping("/sync/{day}")
    public AjaxResult sync(@PathVariable("day")Integer day)
    {
        return success("成功同步订单："+orderOrderService.sync(day)+"条");
    }

    /**
     * 按商户分表同步小红书订单
     */
    @PreAuthorize("@ss.hasPermi('order:order:add')")
    @Log(title = "小红书订单分表同步", businessType = BusinessType.INSERT)
    @GetMapping("/sync/{day}/{sellerId}")
    public AjaxResult syncBySeller(@PathVariable("day") Integer day, @PathVariable("sellerId") String sellerId)
    {
        return success("成功同步订单到分表 t_order_" + sellerId + "：" + orderOrderService.sync(day, sellerId) + "条");
    }

    /**
     * 修改小红书订单
     */
    @PreAuthorize("@ss.hasPermi('order:order:edit')")
    @Log(title = "小红书订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrderOrder orderOrder)
    {
        return toAjax(orderOrderService.updateOrderOrder(orderOrder));
    }

    /**
     * 删除小红书订单
     */
    @PreAuthorize("@ss.hasPermi('order:order:remove')")
    @Log(title = "小红书订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable String[] orderIds)
    {
        return toAjax(orderOrderService.deleteOrderOrderByOrderIds(orderIds));
    }
}
