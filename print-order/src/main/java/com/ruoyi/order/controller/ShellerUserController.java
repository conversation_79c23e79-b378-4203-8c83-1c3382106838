package com.ruoyi.order.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.order.domain.ShellerUser;
import com.ruoyi.order.service.IShellerUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商家用户中间Controller
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
@RestController
@RequestMapping("/shellerUser/shellerUser")
public class ShellerUserController extends BaseController
{
    @Autowired
    private IShellerUserService shellerUserService;

    /**
     * 查询商家用户中间列表
     */
    @PreAuthorize("@ss.hasPermi('shellerUser:shellerUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShellerUser shellerUser)
    {
        startPage();
        List<ShellerUser> list = shellerUserService.selectShellerUserList(shellerUser);
        return getDataTable(list);
    }

    /**
     * 导出商家用户中间列表
     */
    @PreAuthorize("@ss.hasPermi('shellerUser:shellerUser:export')")
    @Log(title = "商家用户中间", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShellerUser shellerUser)
    {
        List<ShellerUser> list = shellerUserService.selectShellerUserList(shellerUser);
        ExcelUtil<ShellerUser> util = new ExcelUtil<ShellerUser>(ShellerUser.class);
        util.exportExcel(response, list, "商家用户中间数据");
    }

    /**
     * 获取商家用户中间详细信息
     */
    @PreAuthorize("@ss.hasPermi('shellerUser:shellerUser:query')")
    @GetMapping(value = "/{shellerId}")
    public AjaxResult getInfo(@PathVariable("shellerId") String shellerId)
    {
        return success(shellerUserService.selectShellerUserByShellerId(shellerId));
    }

    /**
     * 新增商家用户中间
     */
    @PreAuthorize("@ss.hasPermi('shellerUser:shellerUser:add')")
    @Log(title = "商家用户中间", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShellerUser shellerUser)
    {
        return toAjax(shellerUserService.insertShellerUser(shellerUser));
    }

    /**
     * 修改商家用户中间
     */
    @PreAuthorize("@ss.hasPermi('shellerUser:shellerUser:edit')")
    @Log(title = "商家用户中间", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShellerUser shellerUser)
    {
        return toAjax(shellerUserService.updateShellerUser(shellerUser));
    }

    /**
     * 删除商家用户中间
     */
    @PreAuthorize("@ss.hasPermi('shellerUser:shellerUser:remove')")
    @Log(title = "商家用户中间", businessType = BusinessType.DELETE)
	@DeleteMapping("/{shellerIds}")
    public AjaxResult remove(@PathVariable String[] shellerIds)
    {
        return toAjax(shellerUserService.deleteShellerUserByShellerIds(shellerIds));
    }
}
