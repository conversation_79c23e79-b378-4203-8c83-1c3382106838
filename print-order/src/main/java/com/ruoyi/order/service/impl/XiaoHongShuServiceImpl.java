package com.ruoyi.order.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.JwtUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.order.domain.OrderSheller;
import com.ruoyi.order.domain.ShellerUser;
import com.ruoyi.order.service.IShellerUserService;
import com.ruoyi.order.service.OrderShellerService;
import com.ruoyi.order.service.XiaoHongShuService;
import com.ruoyi.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
public class XiaoHongShuServiceImpl implements XiaoHongShuService {
    private static final Logger logger = LoggerFactory.getLogger(XiaoHongShuServiceImpl.class);

    @Autowired
    private RedisCache redisCache;

    @Value("${xiaohongshu.appId}")
    private String appId;
    @Value("${xiaohongshu.version}")
    private String version;
    @Value("${xiaohongshu.appSecret}")
    private String appSecret;
    @Value("${xiaohongshu.url}")
    private String url;
    @Value("${xiaohongshu.privateKey}")
    private String privateKey;
    @Autowired
    private OrderShellerService orderShellerService;
    @Autowired
    private IShellerUserService shellerUserService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private SysLoginService loginService;
    /**
     * 小红书api通用请求接口
     * @param method
     * @param jsonObject
     * @return
     */
    @Override
    public String xiaoHongShuSend(String method, JSONObject jsonObject) {
        // 获取签名
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = signMethod(method, appId, timestamp, version, appSecret);
        jsonObject.put("sign", sign);
        jsonObject.put("timestamp", timestamp);
        jsonObject.put("appId", appId);
        jsonObject.put("version", version);
        //根据商家Id获取redis中的accessToken
        String shellerId = shellerUserService.selectShellerUserByUserId(SecurityUtils.getUserId());
        String tokenRedisKey = "xiaohongshu:access_token:" + shellerId;
        String accessToken = redisCache.getCacheObject(tokenRedisKey);
        if(accessToken==null){
            throw new ServiceException("accessToken已过期，请重新登录");
        }
        jsonObject.put("accessToken", accessToken);
        jsonObject.put("method", method);
        String jsonBody = jsonObject.toString();
        // 使用hutool发送POST请求，body中使用raw JSON方式
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/json")  // 设置Content-Type为JSON
                .body(jsonBody)  // 使用raw body方式提交JSON数据
                .timeout(10000)  // 设置超时时间10秒
                .execute();
        String responseBody = response.body();
        return responseBody;
    }

    /**
     * 小红书回调接口
     * @param code
     * @param method
     */
    @Override
    @Transactional
    public AjaxResult redirect(String code, String method) {
        try {
            // 获取签名
            String timestamp = String.valueOf(System.currentTimeMillis());
            String sign = signMethod(method, appId, timestamp, version, appSecret);
            // 构建请求参数JSON对象
            JSONObject requestBody = new JSONObject();
            requestBody.put("appId", appId);
            requestBody.put("version", version);
            requestBody.put("appSecret", appSecret);
            requestBody.put("code", code);
            requestBody.put("timestamp", timestamp);
            requestBody.put("sign", sign);
            requestBody.put("method", "oauth.getAccessToken");
            String jsonBody = requestBody.toString();
            // 使用hutool发送POST请求，body中使用raw JSON方式
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")  // 设置Content-Type为JSON
                    .body(jsonBody)  // 使用raw body方式提交JSON数据
                    .timeout(10000)  // 设置超时时间10秒
                    .execute();
            String responseBody = response.body();
            //判断该商家是否有用户信息，如果没有则新增并且直接登录，如果有则直接登录
            AjaxResult ajaxResult = xiaoHongShuLogin(responseBody);
            // 解析响应并存储到Redis以及mysql
            parseAndStoreToRedis(responseBody,ajaxResult.get("UserId").toString());

            return ajaxResult;
        } catch (Exception e) {
            logger.error("调用小红书接口异常", e);
            return null;
        }
    }

    private AjaxResult xiaoHongShuLogin(String responseBody) {
        JSONObject responseJson = JSONUtil.parseObj(responseBody);
        if (!isResponseSuccess(responseJson)) {
            String errorMsg = responseJson.getStr("message", "未知错误");
            throw new ServiceException("小红书接口调用失败: "+errorMsg);
        }
        JSONObject data = responseJson.getJSONObject("data");
        String sellerId = data.getStr("sellerId");
        ShellerUser shellerUser = shellerUserService.selectShellerUserByShellerId(sellerId);
        AjaxResult ajax = AjaxResult.success();
        if (shellerUser == null) {
            SysUser sysUser = new SysUser();
            sysUser.setNickName(data.getStr("sellerName"));
            sysUser.setUserName(sellerId);
            //使用随机数生成10位数密码(已废除)
            //String password = String.valueOf(Math.random() * 10000000000L);
            //使用privateKey+sellerId+sellerName生成密码
            String password = privateKey + sellerId + "Yxkj2025";
            sysUser.setPassword(SecurityUtils.encryptPassword(password));
            userService.insertUser(sysUser);
            //调用登录接口直接登录
            String token = loginService.login(sellerId, password, "", "");
            ajax.put("UserId", sysUser.getUserId());
            ajax.put(Constants.TOKEN, token);
        }else {
            //调用登录接口直接登录
            String password = privateKey + sellerId + "Yxkj2025";
            String token = loginService.login(sellerId, password, "", "");
            ajax.put("UserId", shellerUser.getUserId());
            ajax.put(Constants.TOKEN, token);
        }
        return ajax;
    }

    /**
     * 小红书刷新令牌接口
     */
    @Override
    public String refreshAccessToken() {
        // 获取签名
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = signMethod("oauth.refreshToken", appId, timestamp, version, appSecret);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sign", sign);
        jsonObject.put("timestamp", timestamp);
        jsonObject.put("appId", appId);
        jsonObject.put("version", version);
        jsonObject.put("method", "oauth.refreshToken");
        String jsonBody = jsonObject.toString();
        // 使用hutool发送POST请求，body中使用raw JSON方式
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/json")  // 设置Content-Type为JSON
                .body(jsonBody)  // 使用raw body方式提交JSON数据
                .timeout(10000)  // 设置超时时间10秒
                .execute();
        String responseBody = response.body();
        return responseBody;
    }

    /**
     * 解析响应并存储到Redis
     */
    private void parseAndStoreToRedis(String responseBody,String userId) {
        try {
            JSONObject responseJson = JSONUtil.parseObj(responseBody);

            // 检查响应是否成功
            if (!isResponseSuccess(responseJson)) {
                String errorMsg = responseJson.getStr("message", "未知错误");
                throw new ServiceException("小红书接口调用失败: "+errorMsg);
            }

            // 获取并验证data部分
            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                logger.warn("响应中缺少data字段");
                return;
            }

            // 提取必要字段
            String sellerId = data.getStr("sellerId");
            String accessToken = data.getStr("accessToken");
            String refreshToken = data.getStr("refreshToken");
            String sellerName = data.getStr("sellerName");

            if (sellerId == null || accessToken == null) {
                logger.warn("响应中缺少sellerId或accessToken字段");
                return;
            }
            // 处理商家信息
            handleSellerInfo(sellerId, sellerName,userId);
            // 存储token到Redis
            storeTokenToRedis(sellerId, accessToken,refreshToken);
        } catch (Exception e) {
            logger.error("解析小红书响应失败", e);
        }
    }

    /**
     * 检查响应是否成功
     */
    private boolean isResponseSuccess(JSONObject responseJson) {
        return responseJson.containsKey("success") && responseJson.getBool("success");
    }

    /**
     * 处理商家信息
     */
    private void handleSellerInfo(String sellerId, String sellerName,String userId) {
        OrderSheller orderSheller = orderShellerService.selectTOrderShellerBySellerId(sellerId);
        if (orderSheller == null) {
            // 没有这个商家，新增
            orderSheller = new OrderSheller();
            orderSheller.setSellerId(sellerId);
            orderSheller.setBugVersion(DictUtils.getDictValue("bug_version", "免费版"));
            //设置过期时间为7天后
            orderSheller.setExpirationTime(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000));
            orderSheller.setSellerName(sellerName);
            orderShellerService.insertTOrderSheller(orderSheller);
            //新增中间表信息
            ShellerUser shellerUser = new ShellerUser();
            shellerUser.setShellerId(sellerId);
            shellerUser.setUserId(Long.valueOf(userId));
            shellerUserService.insertShellerUser(shellerUser);
        }
    }

    /**
     * 存储token到Redis
     */
    private void storeTokenToRedis(String sellerId, String accessToken,String refreshToken) {
        String redisKey = "xiaohongshu:access_token:" + sellerId;
        redisCache.setCacheObject(redisKey, accessToken, 7, TimeUnit.DAYS);
        String redisRefreshTokenKey = "xiaohongshu:refresh_token:" + sellerId;
        redisCache.setCacheObject(redisRefreshTokenKey, refreshToken, 14, TimeUnit.DAYS);
        logger.info("成功将accessToken存储到Redis，sellerId: {}, key: {}", sellerId, redisKey);
    }

    /**
     * 签名方法
     */
    private String signMethod(String method,String appId,String timestamp,String version,String appSecret) {
        String sign = "";
        sign = method+"?appId="+ appId+"&timestamp=" + timestamp+"&version=" + version+appSecret;
        //md5加密
        sign = Md5Utils.hash(sign);
        return sign;
    }
}
