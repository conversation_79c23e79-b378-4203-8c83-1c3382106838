package com.ruoyi.order.service;

import java.util.List;
import com.ruoyi.order.domain.Commodity;

/**
 * 商品Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
public interface ICommodityService 
{
    /**
     * 查询商品
     * 
     * @param skuId 商品主键
     * @return 商品
     */
    public Commodity selectCommodityBySkuId(String skuId);

    /**
     * 查询商品列表
     * 
     * @param commodity 商品
     * @return 商品集合
     */
    public List<Commodity> selectCommodityList(Commodity commodity);

    /**
     * 新增商品
     * 
     * @param commodity 商品
     * @return 结果
     */
    public int insertCommodity(Commodity commodity);

    /**
     * 修改商品
     * 
     * @param commodity 商品
     * @return 结果
     */
    public int updateCommodity(Commodity commodity);

    /**
     * 批量删除商品
     * 
     * @param skuIds 需要删除的商品主键集合
     * @return 结果
     */
    public int deleteCommodityBySkuIds(String[] skuIds);

    /**
     * 删除商品信息
     * 
     * @param skuId 商品主键
     * @return 结果
     */
    public int deleteCommodityBySkuId(String skuId);

    void batchInsertCommodity(List<Commodity> commodityList);
}
