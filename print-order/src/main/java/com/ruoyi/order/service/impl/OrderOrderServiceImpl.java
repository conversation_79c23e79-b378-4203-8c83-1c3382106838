package com.ruoyi.order.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.order.domain.Commodity;
import com.ruoyi.order.service.ICommodityService;
import com.ruoyi.order.service.IShellerUserService;
import com.ruoyi.order.service.XiaoHongShuService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.order.mapper.OrderOrderMapper;
import com.ruoyi.order.domain.OrderOrder;
import com.ruoyi.order.service.IOrderOrderService;
import org.springframework.transaction.annotation.Transactional;

import static com.ruoyi.common.utils.PageUtils.startPage;

/**
 * 小红书订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
@Service
public class OrderOrderServiceImpl implements IOrderOrderService
{
    private static final Logger logger = LoggerFactory.getLogger(OrderOrderServiceImpl.class);

    @Autowired
    private IShellerUserService shellerUserService;
    @Autowired
    private OrderOrderMapper orderOrderMapper;
    @Autowired
    private XiaoHongShuService xiaoHongShuService;
    @Autowired
    private ICommodityService commodityService;
    /**
     * 查询小红书订单
     * 
     * @param orderId 小红书订单主键
     * @return 小红书订单
     */
    @Override
    public OrderOrder selectOrderOrderByOrderId(String orderId)
    {
        return orderOrderMapper.selectOrderOrderByOrderId(orderId);
    }

    /**
     * 查询小红书订单列表
     * 
     * @param orderOrder 小红书订单
     * @return 小红书订单
     */
    @Override
    public List<OrderOrder> selectOrderOrderList(OrderOrder orderOrder)
    {
        String sellerId = shellerUserService.selectShellerUserByUserId(SecurityUtils.getUserId());
        // 生成表名
        String tableName = "t_order_" + sellerId;
        startPage();
        return orderOrderMapper.selectOrderOrderList(orderOrder,tableName);
    }

    /**
     * 新增小红书订单
     * 
     * @param orderOrder 小红书订单
     * @return 结果
     */
    @Override
    public int insertOrderOrder(OrderOrder orderOrder)
    {
        return orderOrderMapper.insertOrderOrder(orderOrder);
    }

    /**
     * 修改小红书订单
     *
     * @param orderOrder 小红书订单
     * @return 结果
     */
    @Override
    public int updateOrderOrder(OrderOrder orderOrder)
    {
        orderOrder.setUpdateTime(DateUtils.getNowDate().getTime());
        return orderOrderMapper.updateOrderOrder(orderOrder);
    }

    /**
     * 批量删除小红书订单
     * 
     * @param orderIds 需要删除的小红书订单主键
     * @return 结果
     */
    @Override
    public int deleteOrderOrderByOrderIds(String[] orderIds)
    {
        return orderOrderMapper.deleteOrderOrderByOrderIds(orderIds);
    }

    /**
     * 删除小红书订单信息
     * 
     * @param orderId 小红书订单主键
     * @return 结果
     */
    @Override
    public int deleteOrderOrderByOrderId(String orderId)
    {
        return orderOrderMapper.deleteOrderOrderByOrderId(orderId);
    }

    @Override
    @Transactional
    public int sync(Integer day) {
        String sellerId = shellerUserService.selectShellerUserByUserId(SecurityUtils.getUserId());
        return sync(day, sellerId);
    }

    /**
     * 同步订单（指定商户）
     */
    @Override
    @Transactional
    public int sync(Integer day, String sellerId) {
        //统计当前所有订单
        List<OrderOrder> allOrderList = new ArrayList<>();
        //获得当前时间的时间戳
        long currentTimeMillis = System.currentTimeMillis();
        //定义count
        Integer total = 0;
        day = 30;
        while (day>=0){
            int pageNo = 1;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("pageNo", pageNo);
            jsonObject.put("limit", 100);
            jsonObject.put("endTime", currentTimeMillis);
            //把currentTimeMillis这个时间戳往前推24小时
            currentTimeMillis = currentTimeMillis - 24 * 60 * 60 * 1000;
            jsonObject.put("startTime", currentTimeMillis);
            jsonObject.put("timeType", 1);
            //调用小红书订单列表接口
            String responseBodyStr = xiaoHongShuService.xiaoHongShuSend("order.getOrderList", jsonObject);
            //将responseBodyStr转为jsonObject
            JSONObject responseBody = JSONUtil.parseObj(responseBodyStr);
            //判断是否成功
            if (!responseBody.getBool("success")) {
                throw new ServiceException("小红书获取订单列表接口异常");
            }
            //获取total
            if(total==0){
                total = responseBody.getJSONObject("data").getInt("total");
            }
            total = total - 100;
            if(total<=0){
                day--;
            }
            JSONArray orderListJson = responseBody.getJSONObject("data").getJSONArray("orderList");
            //orderListJson转为List<OrderOrder>，需要处理时间戳转换
            if (!orderListJson.isEmpty()) {
                //将orderListJson转为List<OrderOrder>
                List<OrderOrder> orderOrderList = JSON.parseObject(JSON.toJSONString(orderListJson), new TypeReference<List<OrderOrder>>() {
                });

                // 如果指定了sellerId，为每个订单设置sellerId
                if (sellerId != null && !sellerId.trim().isEmpty()) {
                    for (OrderOrder order : orderOrderList) {
                        order.setSellerId(sellerId);
                    }
                }

                allOrderList.addAll(orderOrderList);
            }
            day--;
        }
        //遍历allOrderList调用小红书查询订单详情接口
        for (OrderOrder order : allOrderList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("orderId",order.getOrderId());
            //调用小红书订单详情接口
            String responseBodyStr = xiaoHongShuService.xiaoHongShuSend("order.getOrderDetail", jsonObject);
            JSONObject responseBody = JSONUtil.parseObj(responseBodyStr);
            if (!responseBody.getBool("success")) {
                throw new ServiceException("小红书获取订单详情接口异常");
            }
            JSONObject data = responseBody.getJSONObject("data");
            order.setUserId(data.getStr("userId"));
            order.setOpenAddressId(data.getStr("openAddressId"));
            //获取商品详情
            JSONArray commodityJSONList = data.getJSONArray("skuList");
            List<Commodity> commodityList = new ArrayList<>();
            commodityJSONList.forEach(item->{
                JSONObject commodityJsonObject = (JSONObject) item;
                Commodity commodity = new Commodity();
                commodity.setOrderId(order.getOrderId());
                commodity.setSkuId(commodityJsonObject.getStr("skuId"));
                commodity.setSkuName(commodityJsonObject.getStr("skuName"));
                commodity.setSkuImage(commodityJsonObject.getStr("skuImage"));
                commodity.setSkuQuantity(commodityJsonObject.getStr("skuQuantity"));
                commodity.setTotalPaidAmount(commodityJsonObject.getBigDecimal("totalPaidAmount"));
                commodity.setErpCode(commodityJsonObject.getStr("erpCode"));
                commodityList.add(commodity);
            });
            commodityService.batchInsertCommodity(commodityList);
            //调用小红书订单收件人信息接口
            jsonObject = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            JSONObject responseJsonObject = new JSONObject();
            responseJsonObject.set("openAddressId",order.getOpenAddressId());
            responseJsonObject.set("orderId",order.getOrderId());
            jsonArray.add(responseJsonObject);
            jsonObject.set("receiverQueries",jsonArray);
            //小红书官方订单类型为5则为换货订单
            if(order.getOrderType().equals(5L)){
                jsonObject.set("isReturn",true);
            }else {
                jsonObject.set("isReturn",false);
            }
            String receiverResponseBodyStr = xiaoHongShuService.xiaoHongShuSend("order.getOrderReceiverInfo", jsonObject);
            System.out.println(jsonObject);
            JSONObject receiverResponseBody = JSONUtil.parseObj(receiverResponseBodyStr);
            if (receiverResponseBody.getStr("error_code").equals("-9020")){
                continue;
            }
            if (!receiverResponseBody.getBool("success")) {
                throw new ServiceException("小红书获取订单收件人信息接口异常");
            }
            JSONObject receiverData = receiverResponseBody.getJSONObject("data");
            JSONArray receiverInfos = receiverData.getJSONArray("receiverInfos");
            JSONObject receiverInfo = receiverInfos.getJSONObject(0);
            if(receiverInfo==null){
                continue;
            }
            order.setReceiverName(receiverInfo.getStr("receiverName"));
            order.setReceiverPhone(receiverInfo.getStr("receiverPhone"));
            order.setReceiverAddress(receiverInfo.getStr("receiverAddress"));
            //调用小红书解密接口
            /*jsonObject = new JSONObject();
            jsonArray = new JSONArray();
            for (int i =0;i<3;i++){
                JSONObject baseInfosJson = new JSONObject();
                baseInfosJson.set("dataTag",order.getOrderId());
                if (i==0){
                    baseInfosJson.set("encryptedData",receiverInfo.getStr("receiverName"));
                }
                if (i==1){
                    baseInfosJson.set("encryptedData",receiverInfo.getStr("receiverPhone"));
                }
                if (i==2){
                    baseInfosJson.set("encryptedData",receiverInfo.getStr("receiverAddress"));
                }
                jsonArray.add(baseInfosJson);
            }
            jsonObject.set("baseInfos",jsonArray);
            jsonObject.set("actionType","1");
            jsonObject.set("appUserId",111111);
            String decryptResponseBodyStr = xiaoHongShuService.xiaoHongShuSend("data.batchDecrypt", jsonObject);
            JSONObject decryptResponseBody = JSONUtil.parseObj(decryptResponseBodyStr);
            if (!decryptResponseBody.getBool("success")) {
                throw new ServiceException("小红书解密接口异常");
            }
            JSONArray dataInfoList = decryptResponseBody.getJSONObject("data").getJSONArray("dataInfoList");
            for (int i = 0; i < 3; i++) {
                if (dataInfoList.getJSONObject(i)==null){
                    continue;
                }
                if (dataInfoList.getJSONObject(i).getStr("decryptedData")==null){
                    continue;
                }
                if (i == 0) {
                    order.setReceiverName(dataInfoList.getJSONObject(i).getStr("decryptedData"));
                }
                if (i == 1) {
                    order.setReceiverPhone(dataInfoList.getJSONObject(i).getStr("decryptedData"));
                }
                if (i == 2) {
                    order.setReceiverAddress(dataInfoList.getJSONObject(i).getStr("decryptedData"));
                }
            }*/
        }
        // 按商户分组并分表存储
        return batchInsertOrdersBySellerTable(allOrderList);
    }

    /**
     * 按商户分表批量插入订单
     */
    private int batchInsertOrdersBySellerTable(List<OrderOrder> allOrderList) {
        if (allOrderList.isEmpty()) {
            return 0;
        }

        // 按sellerId分组订单（需要从订单数据中提取sellerId）
        Map<String, List<OrderOrder>> sellerOrderMap = groupOrdersBySeller(allOrderList);

        int totalInserted = 0;

        for (Map.Entry<String, List<OrderOrder>> entry : sellerOrderMap.entrySet()) {
            String sellerId = entry.getKey();
            List<OrderOrder> orderList = entry.getValue();

            if (sellerId == null || sellerId.trim().isEmpty()) {
                logger.warn("发现sellerId为空的订单，跳过分表处理，订单数量: {}", orderList.size());
                continue;
            }

            try {
                // 生成表名
                String tableName = "t_order_" + sellerId;

                // 创建分表（如果不存在）
                createTableIfNotExists(tableName, sellerId);

                // 批量插入到对应的分表
                int inserted = orderOrderMapper.batchInsertOrderOrderByTable(tableName, orderList);
                totalInserted += inserted;

                logger.info("成功插入订单到分表 {}, 插入数量: {}", tableName, inserted);

            } catch (Exception e) {
                logger.error("插入订单到分表失败，sellerId: {}, 订单数量: {}", sellerId, orderList.size(), e);
                // 继续处理其他商户的订单，不中断整个流程
            }
        }

        return totalInserted;
    }

    /**
     * 按商户分组订单
     */
    private Map<String, List<OrderOrder>> groupOrdersBySeller(List<OrderOrder> orderList) {
        Map<String, List<OrderOrder>> sellerOrderMap = new HashMap<>();

        for (OrderOrder order : orderList) {
            // 从订单中提取sellerId，这里需要根据实际的订单数据结构来获取
            // 假设订单中有sellerId字段，如果没有需要从其他地方获取
            String sellerId = extractSellerIdFromOrder(order);

            if (sellerId != null && !sellerId.trim().isEmpty()) {
                sellerOrderMap.computeIfAbsent(sellerId, k -> new ArrayList<>()).add(order);
            } else {
                logger.warn("订单 {} 缺少sellerId信息", order.getOrderId());
            }
        }

        return sellerOrderMap;
    }

    /**
     * 从订单中提取sellerId
     */
    private String extractSellerIdFromOrder(OrderOrder order) {
        // 现在OrderOrder实体类中已经有sellerId字段
        String sellerId = order.getSellerId();

        if (sellerId != null && !sellerId.trim().isEmpty()) {
            return sellerId;
        }

        // 如果sellerId为空，记录警告并返回null
        logger.warn("订单 {} 的sellerId为空", order.getOrderId());
        return null;
    }

    /**
     * 创建分表（如果不存在）
     */
    private void createTableIfNotExists(String tableName, String sellerId) {
        try {
            orderOrderMapper.createOrderTable(tableName, sellerId);
            logger.info("成功创建或确认分表存在: {}", tableName);
        } catch (Exception e) {
            logger.error("创建分表失败: {}", tableName, e);
            throw new ServiceException("创建分表失败: " + tableName);
        }
    }


}
