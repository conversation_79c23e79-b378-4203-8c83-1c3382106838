package com.ruoyi.order.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.order.mapper.ShellerUserMapper;
import com.ruoyi.order.domain.ShellerUser;
import com.ruoyi.order.service.IShellerUserService;

/**
 * 商家用户中间Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
@Service
public class ShellerUserServiceImpl implements IShellerUserService 
{
    @Autowired
    private ShellerUserMapper shellerUserMapper;

    /**
     * 查询商家用户中间
     * 
     * @param shellerId 商家用户中间主键
     * @return 商家用户中间
     */
    @Override
    public ShellerUser selectShellerUserByShellerId(String shellerId)
    {
        return shellerUserMapper.selectShellerUserByShellerId(shellerId);
    }

    /**
     * 查询商家用户中间列表
     * 
     * @param shellerUser 商家用户中间
     * @return 商家用户中间
     */
    @Override
    public List<ShellerUser> selectShellerUserList(ShellerUser shellerUser)
    {
        return shellerUserMapper.selectShellerUserList(shellerUser);
    }

    /**
     * 新增商家用户中间
     * 
     * @param shellerUser 商家用户中间
     * @return 结果
     */
    @Override
    public int insertShellerUser(ShellerUser shellerUser)
    {
        return shellerUserMapper.insertShellerUser(shellerUser);
    }

    /**
     * 修改商家用户中间
     * 
     * @param shellerUser 商家用户中间
     * @return 结果
     */
    @Override
    public int updateShellerUser(ShellerUser shellerUser)
    {
        return shellerUserMapper.updateShellerUser(shellerUser);
    }

    /**
     * 批量删除商家用户中间
     * 
     * @param shellerIds 需要删除的商家用户中间主键
     * @return 结果
     */
    @Override
    public int deleteShellerUserByShellerIds(String[] shellerIds)
    {
        return shellerUserMapper.deleteShellerUserByShellerIds(shellerIds);
    }

    /**
     * 删除商家用户中间信息
     * 
     * @param shellerId 商家用户中间主键
     * @return 结果
     */
    @Override
    public int deleteShellerUserByShellerId(String shellerId)
    {
        return shellerUserMapper.deleteShellerUserByShellerId(shellerId);
    }

    @Override
    public String selectShellerUserByUserId(Long userId) {
        return shellerUserMapper.selectShellerUserByUserId(userId);
    }
}
