package com.ruoyi.order.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.order.mapper.OrderShellerMapper;
import com.ruoyi.order.domain.OrderSheller;
import com.ruoyi.order.service.OrderShellerService;

/**
 * 小红书商家信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Service
public class OrderShellerServiceImpl implements OrderShellerService
{
    @Autowired
    private OrderShellerMapper orderShellerMapper;

    /**
     * 查询小红书商家信息
     * 
     * @param sellerId 小红书商家信息主键
     * @return 小红书商家信息
     */
    @Override
    public OrderSheller selectTOrderShellerBySellerId(String sellerId)
    {
        return orderShellerMapper.selectTOrderShellerBySellerId(sellerId);
    }

    /**
     * 查询小红书商家信息列表
     * 
     * @param orderSheller 小红书商家信息
     * @return 小红书商家信息
     */
    @Override
    public List<OrderSheller> selectTOrderShellerList(OrderSheller orderSheller)
    {
        return orderShellerMapper.selectTOrderShellerList(orderSheller);
    }

    /**
     * 新增小红书商家信息
     *
     * @param orderSheller 小红书商家信息
     * @return 结果
     */
    @Override
    public int insertTOrderSheller(OrderSheller orderSheller)
    {
        orderSheller.setCreateTime(DateUtils.getNowDate());
        orderSheller.setDelFlag("0"); // 设置删除标志为0（正常）
        return orderShellerMapper.insertTOrderSheller(orderSheller);
    }

    /**
     * 修改小红书商家信息
     * 
     * @param orderSheller 小红书商家信息
     * @return 结果
     */
    @Override
    public int updateTOrderSheller(OrderSheller orderSheller)
    {
        orderSheller.setUpdateTime(DateUtils.getNowDate());
        return orderShellerMapper.updateTOrderSheller(orderSheller);
    }

    /**
     * 批量删除小红书商家信息（逻辑删除）
     *
     * @param sellerIds 需要删除的小红书商家信息主键
     * @return 结果
     */
    @Override
    public int deleteTOrderShellerBySellerIds(String[] sellerIds)
    {
        int result = 0;
        for (String sellerId : sellerIds) {
            result += deleteTOrderShellerBySellerId(sellerId);
        }
        return result;
    }

    /**
     * 删除小红书商家信息信息（逻辑删除）
     *
     * @param sellerId 小红书商家信息主键
     * @return 结果
     */
    @Override
    public int deleteTOrderShellerBySellerId(String sellerId)
    {
        OrderSheller orderSheller = new OrderSheller();
        orderSheller.setSellerId(sellerId);
        orderSheller.setDelFlag("2"); // 设置删除标志为2（已删除）
        orderSheller.setUpdateTime(DateUtils.getNowDate());
        return orderShellerMapper.updateTOrderSheller(orderSheller);
    }
}
