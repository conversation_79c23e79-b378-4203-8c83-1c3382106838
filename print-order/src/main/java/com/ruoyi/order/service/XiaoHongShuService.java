package com.ruoyi.order.service;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;

public interface XiaoHongShuService {
    /**
     * 小红书api通用请求接口
     * @param method
     * @param jsonObject
     * @return
     */
    public String xiaoHongShuSend( String method, JSONObject jsonObject);

    /**
     * 小红书回调接口
     * @param code
     * @param method
     */
    public AjaxResult redirect(String code, String method);


    public String refreshAccessToken();

}
