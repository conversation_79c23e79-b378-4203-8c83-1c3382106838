package com.ruoyi.order.service;

import java.util.List;
import com.ruoyi.order.domain.OrderOrder;

/**
 * 小红书订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public interface IOrderOrderService 
{
    /**
     * 查询小红书订单
     * 
     * @param orderId 小红书订单主键
     * @return 小红书订单
     */
    public OrderOrder selectOrderOrderByOrderId(String orderId);

    /**
     * 查询小红书订单列表
     * 
     * @param orderOrder 小红书订单
     * @return 小红书订单集合
     */
    public List<OrderOrder> selectOrderOrderList(OrderOrder orderOrder);

    /**
     * 新增小红书订单
     * 
     * @param orderOrder 小红书订单
     * @return 结果
     */
    public int insertOrderOrder(OrderOrder orderOrder);

    /**
     * 修改小红书订单
     * 
     * @param orderOrder 小红书订单
     * @return 结果
     */
    public int updateOrderOrder(OrderOrder orderOrder);

    /**
     * 批量删除小红书订单
     * 
     * @param orderIds 需要删除的小红书订单主键集合
     * @return 结果
     */
    public int deleteOrderOrderByOrderIds(String[] orderIds);

    /**
     * 删除小红书订单信息
     * 
     * @param orderId 小红书订单主键
     * @return 结果
     */
    public int deleteOrderOrderByOrderId(String orderId);

    int sync(Integer day);

    /**
     * 同步订单（指定商户）
     * @param day 天数
     * @param sellerId 商户ID
     * @return
     */
    int sync(Integer day, String sellerId);
}
