package com.ruoyi.order.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.order.mapper.CommodityMapper;
import com.ruoyi.order.domain.Commodity;
import com.ruoyi.order.service.ICommodityService;

/**
 * 商品Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
public class CommodityServiceImpl implements ICommodityService 
{
    @Autowired
    private CommodityMapper commodityMapper;

    /**
     * 查询商品
     * 
     * @param skuId 商品主键
     * @return 商品
     */
    @Override
    public Commodity selectCommodityBySkuId(String skuId)
    {
        return commodityMapper.selectCommodityBySkuId(skuId);
    }

    /**
     * 查询商品列表
     * 
     * @param commodity 商品
     * @return 商品
     */
    @Override
    public List<Commodity> selectCommodityList(Commodity commodity)
    {
        return commodityMapper.selectCommodityList(commodity);
    }

    /**
     * 新增商品
     * 
     * @param commodity 商品
     * @return 结果
     */
    @Override
    public int insertCommodity(Commodity commodity)
    {
        return commodityMapper.insertCommodity(commodity);
    }

    /**
     * 修改商品
     * 
     * @param commodity 商品
     * @return 结果
     */
    @Override
    public int updateCommodity(Commodity commodity)
    {
        return commodityMapper.updateCommodity(commodity);
    }

    /**
     * 批量删除商品
     * 
     * @param skuIds 需要删除的商品主键
     * @return 结果
     */
    @Override
    public int deleteCommodityBySkuIds(String[] skuIds)
    {
        return commodityMapper.deleteCommodityBySkuIds(skuIds);
    }

    /**
     * 删除商品信息
     * 
     * @param skuId 商品主键
     * @return 结果
     */
    @Override
    public int deleteCommodityBySkuId(String skuId)
    {
        return commodityMapper.deleteCommodityBySkuId(skuId);
    }

    @Override
    public void batchInsertCommodity(List<Commodity> commodityList) {
        commodityMapper.batchInsertCommodity(commodityList);

    }
}
