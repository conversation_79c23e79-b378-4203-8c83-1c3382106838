package com.ruoyi.order.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 商家用户中间对象 t_sheller_user
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public class ShellerUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 商家id */
    @Excel(name = "商家id")
    private String shellerId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    public ShellerUser() {

    }

    public void setShellerId(String shellerId) 
    {
        this.shellerId = shellerId;
    }

    public String getShellerId() 
    {
        return shellerId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("shellerId", getShellerId())
            .append("userId", getUserId())
            .toString();
    }
}
