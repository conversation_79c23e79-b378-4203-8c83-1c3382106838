package com.ruoyi.order.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 小红书商家信息对象 t_order_sheller
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class OrderSheller extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 商家Id（小红书） */
    private String sellerId;

    /** 商家名 */
    @Excel(name = "商家名")
    private String sellerName;

    /** 到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expirationTime;

    /** 是否下次弹出（首页弹出信息) */
    @Excel(name = "是否下次弹出", readConverterExp = "是否下次弹出（首页弹出信息)")
    private Long isPopup;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** bug版本 */
    @Excel(name = "bug版本")
    private String bugVersion;

    public void setSellerId(String sellerId) 
    {
        this.sellerId = sellerId;
    }

    public String getSellerId() 
    {
        return sellerId;
    }

    public void setSellerName(String sellerName) 
    {
        this.sellerName = sellerName;
    }

    public String getSellerName() 
    {
        return sellerName;
    }

    public void setExpirationTime(Date expirationTime) 
    {
        this.expirationTime = expirationTime;
    }

    public Date getExpirationTime() 
    {
        return expirationTime;
    }

    public void setIsPopup(Long isPopup) 
    {
        this.isPopup = isPopup;
    }

    public Long getIsPopup() 
    {
        return isPopup;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setBugVersion(String bugVersion)
    {
        this.bugVersion = bugVersion;
    }

    public String getBugVersion()
    {
        return bugVersion;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("sellerId", getSellerId())
            .append("sellerName", getSellerName())
            .append("expirationTime", getExpirationTime())
            .append("isPopup", getIsPopup())
            .append("delFlag", getDelFlag())
            .append("bugVersion", getBugVersion())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
