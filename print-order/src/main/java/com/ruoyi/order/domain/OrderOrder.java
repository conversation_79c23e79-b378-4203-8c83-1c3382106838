package com.ruoyi.order.domain;

import java.util.Date;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 小红书订单对象 t_order_order
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
@Data
public class OrderOrder
{
    private static final long serialVersionUID = 1L;

    /** 订单号 */
    private String orderId;

    /** 商户ID */
    private String sellerId;

    /** 订单类型，1普通 2定金预售 3全款预售(废弃) 4全款预售(新) 5换货补发 */
    @Excel(name = "订单类型，1普通 2定金预售 3全款预售(废弃) 4全款预售(新) 5换货补发")
    private Long orderType;

    /** 订单状态，1已下单待付款 2已支付处理中 3清关中 4待发货 5部分发货 6待收货 7已完成 8已关闭 9已取消 10换货申请中 */
    @Excel(name = "订单状态，1已下单待付款 2已支付处理中 3清关中 4待发货 5部分发货 6待收货 7已完成 8已关闭 9已取消 10换货申请中")
    private Long orderStatus;

    /** 售后状态，1无售后 2售后处理中 3售后完成 4售后拒绝 5售后关闭 6平台介入中 7售后取消 */
    @Excel(name = "售后状态，1无售后 2售后处理中 3售后完成 4售后拒绝 5售后关闭 6平台介入中 7售后取消")
    private Long orderAfterSalesStatus;

    /** 申请取消状态，0未申请取消 1取消处理中 */
    @Excel(name = "申请取消状态，0未申请取消 1取消处理中")
    private Long cancelStatus;

    /** 创建时间 */
    private Long createdTime;

    private Long updateTime;

    /** 支付时间 */
    private Long paidTime;

    /** 订单发货时间 */
    private Long deliveryTime;

    /** 订单取消时间 */
    private Long cancelTime;

    /** 订单完成时间 */
    private Long finishTime;

    /** 承诺最晚发货时间 */
    private Long promiseLastDeliveryTime;

    /** 物流方案id */
    @Excel(name = "物流方案id")
    private String planinfoId;

    /** 物流方案名称 */
    @Excel(name = "物流方案名称")
    private String planinfoName;

    /** 收件人国家id */
    @Excel(name = "收件人国家id")
    private String receiverCountryId;

    /** 目前仅 中国 */
    @Excel(name = "目前仅 中国")
    private String receiverCountryName;

    /** 收件人省份id */
    @Excel(name = "收件人省份id")
    private String receiverProvinceId;

    /** 收件人省份 */
    @Excel(name = "收件人省份")
    private String receiverProvinceName;

    /** 收件人城市id */
    @Excel(name = "收件人城市id")
    private String receiverCityId;

    /** 收件人城市 */
    @Excel(name = "收件人城市")
    private String receiverCityName;

    /** 收件人区县id */
    @Excel(name = "收件人区县id")
    private String receiverDistrictId;

    /** 收件人区县名称 */
    @Excel(name = "收件人区县名称")
    private String receiverDistrictName;

    /** 用户备注 */
    @Excel(name = "用户备注")
    private String customerRemark;

    /** 商家标记备注 */
    @Excel(name = "商家标记备注")
    private String sellerRemark;

    /** 商家标记优先级，ark订单列表展示旗子颜色 1灰旗 2红旗 3黄旗 4绿旗 5蓝旗 6紫旗 */
    @Excel(name = "商家标记优先级，ark订单列表展示旗子颜色 1灰旗 2红旗 3黄旗 4绿旗 5蓝旗 6紫旗")
    private Long sellerRemarkFlag;

    /** 原始订单编号，换货订单的原订单 */
    @Excel(name = "原始订单编号，换货订单的原订单")
    private String originalOrderId;

    /** 物流模式 red_express三方备货直邮(备货海外仓),red_domestic_trade(三方备货内贸),red_standard(三方备货保税仓),red_auto(三方自主发货),red_box(三方小包),red_bonded(三方保税) */
    @Excel(name = "物流模式 red_express三方备货直邮(备货海外仓),red_domestic_trade(三方备货内贸),red_standard(三方备货保税仓),red_auto(三方自主发货),red_box(三方小包),red_bonded(三方保税)")
    private String logistics;

    /** 订单标签列表 NEW_YEAR 新年礼 PLATFORM_DECLARE 平台报备 SELLER_DECLARE 商家报备 CONSULT 协商发货 MODIFIED_ADDR 已改地址 MODIFIED_PRICE 已改价 NO_LOGISTICS_SHIP 无物流发货 PRINTED 部分打单/已打单 URGENT_SHIP 催发货 QIC QIC质检 SAMPLE 拿样 HOME_DELIVERY 送货上门 LACK_GOOD 缺货 EXPLODE 发生现货爆单的订单 EXEMPT 发生现货爆单享受豁免 CERTIFICATION_WAREHOUSE 认证仓 COUNTRY_SUBSIDY: 国家补贴 CITY_SUBSIDY:城市补贴 BUY_AGENT:代购 */
    @Excel(name = "订单标签列表 NEW_YEAR 新年礼 PLATFORM_DECLARE 平台报备 SELLER_DECLARE 商家报备 CONSULT 协商发货 MODIFIED_ADDR 已改地址 MODIFIED_PRICE 已改价 NO_LOGISTICS_SHIP 无物流发货 PRINTED 部分打单/已打单 URGENT_SHIP 催发货 QIC QIC质检 SAMPLE 拿样 HOME_DELIVERY 送货上门 LACK_GOOD 缺货 EXPLODE 发生现货爆单的订单 EXEMPT 发生现货爆单享受豁免 CERTIFICATION_WAREHOUSE 认证仓 COUNTRY_SUBSIDY: 国家补贴 CITY_SUBSIDY:城市补贴 BUY_AGENT:代购")
    private String orderTagList;
    /** 用户Id **/
    private String userId;
    /** 收件人姓名+手机+地址等计算得出，用来查询收件人详情 **/
    private String openAddressId;
    /** 收件人姓名 **/
    private String receiverName;
    /** 收件人手机号码 **/
    private String receiverPhone;
    /** 收件人详细地址 **/
    private String receiverAddress;
    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, Object> params;

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }

    public void setOrderType(Long orderType) 
    {
        this.orderType = orderType;
    }

    public Long getOrderType() 
    {
        return orderType;
    }

    public void setOrderStatus(Long orderStatus) 
    {
        this.orderStatus = orderStatus;
    }

    public Long getOrderStatus() 
    {
        return orderStatus;
    }

    public void setOrderAfterSalesStatus(Long orderAfterSalesStatus) 
    {
        this.orderAfterSalesStatus = orderAfterSalesStatus;
    }

    public Long getOrderAfterSalesStatus() 
    {
        return orderAfterSalesStatus;
    }

    public void setCancelStatus(Long cancelStatus) 
    {
        this.cancelStatus = cancelStatus;
    }

    public Long getCancelStatus() 
    {
        return cancelStatus;
    }

    public void setPlaninfoId(String planinfoId) 
    {
        this.planinfoId = planinfoId;
    }

    public String getPlaninfoId() 
    {
        return planinfoId;
    }

    public void setPlaninfoName(String planinfoName) 
    {
        this.planinfoName = planinfoName;
    }

    public String getPlaninfoName() 
    {
        return planinfoName;
    }

    public void setReceiverCountryId(String receiverCountryId) 
    {
        this.receiverCountryId = receiverCountryId;
    }

    public String getReceiverCountryId() 
    {
        return receiverCountryId;
    }

    public void setReceiverCountryName(String receiverCountryName) 
    {
        this.receiverCountryName = receiverCountryName;
    }

    public String getReceiverCountryName() 
    {
        return receiverCountryName;
    }

    public void setReceiverProvinceId(String receiverProvinceId) 
    {
        this.receiverProvinceId = receiverProvinceId;
    }

    public String getReceiverProvinceId() 
    {
        return receiverProvinceId;
    }

    public void setReceiverProvinceName(String receiverProvinceName) 
    {
        this.receiverProvinceName = receiverProvinceName;
    }

    public String getReceiverProvinceName() 
    {
        return receiverProvinceName;
    }

    public void setReceiverCityId(String receiverCityId) 
    {
        this.receiverCityId = receiverCityId;
    }

    public String getReceiverCityId() 
    {
        return receiverCityId;
    }

    public void setReceiverCityName(String receiverCityName) 
    {
        this.receiverCityName = receiverCityName;
    }

    public String getReceiverCityName() 
    {
        return receiverCityName;
    }

    public void setReceiverDistrictId(String receiverDistrictId) 
    {
        this.receiverDistrictId = receiverDistrictId;
    }

    public String getReceiverDistrictId() 
    {
        return receiverDistrictId;
    }

    public void setReceiverDistrictName(String receiverDistrictName) 
    {
        this.receiverDistrictName = receiverDistrictName;
    }

    public String getReceiverDistrictName() 
    {
        return receiverDistrictName;
    }

    public void setCustomerRemark(String customerRemark) 
    {
        this.customerRemark = customerRemark;
    }

    public String getCustomerRemark() 
    {
        return customerRemark;
    }

    public void setSellerRemark(String sellerRemark) 
    {
        this.sellerRemark = sellerRemark;
    }

    public String getSellerRemark() 
    {
        return sellerRemark;
    }

    public void setSellerRemarkFlag(Long sellerRemarkFlag) 
    {
        this.sellerRemarkFlag = sellerRemarkFlag;
    }

    public Long getSellerRemarkFlag() 
    {
        return sellerRemarkFlag;
    }

    public void setOriginalOrderId(String originalOrderId) 
    {
        this.originalOrderId = originalOrderId;
    }

    public String getOriginalOrderId() 
    {
        return originalOrderId;
    }

    public void setLogistics(String logistics) 
    {
        this.logistics = logistics;
    }

    public String getLogistics() 
    {
        return logistics;
    }

    public void setOrderTagList(String orderTagList) 
    {
        this.orderTagList = orderTagList;
    }

    public String getOrderTagList() 
    {
        return orderTagList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("orderId", getOrderId())
            .append("orderType", getOrderType())
            .append("orderStatus", getOrderStatus())
            .append("orderAfterSalesStatus", getOrderAfterSalesStatus())
            .append("cancelStatus", getCancelStatus())
            .append("planinfoId", getPlaninfoId())
            .append("planinfoName", getPlaninfoName())
            .append("receiverCountryId", getReceiverCountryId())
            .append("receiverCountryName", getReceiverCountryName())
            .append("receiverProvinceId", getReceiverProvinceId())
            .append("receiverProvinceName", getReceiverProvinceName())
            .append("receiverCityId", getReceiverCityId())
            .append("receiverCityName", getReceiverCityName())
            .append("receiverDistrictId", getReceiverDistrictId())
            .append("receiverDistrictName", getReceiverDistrictName())
            .append("customerRemark", getCustomerRemark())
            .append("sellerRemark", getSellerRemark())
            .append("sellerRemarkFlag", getSellerRemarkFlag())
            .append("originalOrderId", getOriginalOrderId())
            .append("logistics", getLogistics())
            .append("orderTagList", getOrderTagList())
            .toString();
    }
}
