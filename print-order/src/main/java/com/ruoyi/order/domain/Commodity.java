package com.ruoyi.order.domain;

import java.math.BigDecimal;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 商品对象 t_commodity
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
public class Commodity
{

    /** $column.columnComment */
    private String skuId;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String skuName;

    /** 商家编码 */
    @Excel(name = "商家编码")
    private String erpCode;

    /** 规格 */
    @Excel(name = "规格")
    private String skuSpec;

    /** 商品图片url */
    @Excel(name = "商品图片url")
    private String skuImage;

    /** 商品数量 */
    @Excel(name = "商品数量")
    private String skuQuantity;

    /** 订单id */
    private String orderId;

    /** 总支付金额（考虑总件数）商品总实付 */
    @Excel(name = "总支付金额", readConverterExp = "考=虑总件数")
    private BigDecimal totalPaidAmount;

    public void setSkuId(String skuId) 
    {
        this.skuId = skuId;
    }

    public String getSkuId() 
    {
        return skuId;
    }

    public void setSkuName(String skuName) 
    {
        this.skuName = skuName;
    }

    public String getSkuName() 
    {
        return skuName;
    }

    public void setErpCode(String erpCode) 
    {
        this.erpCode = erpCode;
    }

    public String getErpCode() 
    {
        return erpCode;
    }

    public void setSkuSpec(String skuSpec) 
    {
        this.skuSpec = skuSpec;
    }

    public String getSkuSpec() 
    {
        return skuSpec;
    }

    public void setSkuImage(String skuImage) 
    {
        this.skuImage = skuImage;
    }

    public String getSkuImage() 
    {
        return skuImage;
    }

    public void setSkuQuantity(String skuQuantity) 
    {
        this.skuQuantity = skuQuantity;
    }

    public String getSkuQuantity() 
    {
        return skuQuantity;
    }

    public void setTotalPaidAmount(BigDecimal totalPaidAmount) 
    {
        this.totalPaidAmount = totalPaidAmount;
    }

    public BigDecimal getTotalPaidAmount() 
    {
        return totalPaidAmount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("skuId", getSkuId())
            .append("skuName", getSkuName())
            .append("erpCode", getErpCode())
            .append("skuSpec", getSkuSpec())
            .append("skuImage", getSkuImage())
            .append("skuQuantity", getSkuQuantity())
            .append("totalPaidAmount", getTotalPaidAmount())
            .toString();
    }
}
